import types
import pytest
from types import SimpleNamespace
from unittest.mock import AsyncMock, patch

import importlib.util
from pathlib import Path

ROOT = Path(__file__).resolve().parents[1]
COST_API_QUART_PATH = ROOT / "backend" / "cost_management" / "cost_api_quart.py"

spec = importlib.util.spec_from_file_location("cost_api_quart", COST_API_QUART_PATH)
cost_api = importlib.util.module_from_spec(spec)
spec.loader.exec_module(cost_api)


class MockRequest:
    def __init__(self, headers=None, args=None):
        self.headers = headers or {}
        self.args = args or {}

    async def get_json(self):
        return {}


@pytest.mark.asyncio
async def test_overview_with_minimal_token_user():
    user = {"user_principal_id": "u1", "roles": ["super_admin"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[[100, "project-id", "1", "EUR"]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
    )
    patched_cost_service = SimpleNamespace(
        query_cost_by_tag=AsyncMock(return_value=mock_rows),
        get_project_names_map=AsyncMock(return_value={}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(cost_api, "cost_service", patched_cost_service):
        result = await cost_api.get_cost_overview()
        assert result["totalCost"] == 100.0


@pytest.mark.asyncio
async def test_resources_with_minimal_token_user():
    user = {"user_principal_id": "u1", "roles": ["super_admin"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[[42, "/subscriptions/sub/resourceGroups/rg/providers/Microsoft.Storage/storageAccounts/res1", "EUR"]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api,
        "cost_service",
        SimpleNamespace(query_cost_by_resource=AsyncMock(return_value=mock_rows)),
    ):
        result = await cost_api.get_resource_costs()
        assert result["totalCost"] == 42


@pytest.mark.asyncio
async def test_collect_now_endpoint():
    user = {"user_principal_id": "u1", "roles": ["SuperAdmin"]}
    request = MockRequest(headers={}, args={})
    rbac_client = SimpleNamespace(
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"})
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(cost_api, "run_cost_collection_task", AsyncMock()):
        result = await cost_api.collect_cost_now()
        assert result["message"] == "Cost data collection triggered"


@pytest.mark.asyncio
async def test_role_overridden_from_db():
    token_user = {"user_principal_id": "u1", "roles": ["RegularUser"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[[100, "project-id", "1", "EUR"]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
    )
    patched_cost_service = SimpleNamespace(
        query_cost_by_tag=AsyncMock(return_value=mock_rows),
        get_project_names_map=AsyncMock(return_value={}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=token_user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(cost_api, "cost_service", patched_cost_service):
        result = await cost_api.get_cost_overview()
        assert result["totalCost"] == 100.0
        assert rbac_client.get_user.awaited
