2025-06-19 13:47:09,152 - INFO - RBAC log level set to DEBUG
2025-06-19 13:47:09,162 - INFO - RBAC log level set to DEBUG
2025-06-19 13:47:09,182 - INFO - RBAC log level set to DEBUG
2025-06-19 13:47:09,258 - INFO - RBAC log level set to DEBUG
2025-06-19 13:47:22,937 - INFO - RBAC client initialized successfully.
2025-06-19 13:47:23,306 - INFO - RBAC client initialized successfully.
2025-06-19 13:47:33,463 - INFO - RBAC client initialized successfully.
2025-06-19 13:47:33,463 - INFO - Found current user ID in header: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 13:47:33,463 - INFO - Using mock authentication with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 13:47:33,464 - INFO - get_mock_user returned: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150'}
2025-06-19 13:47:33,619 - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T13:47:23.925485+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00008f79-0000-0d00-0000-685414ec0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750340844}
2025-06-19 13:47:33,620 - INFO - Set user role from database to: SUPER_ADMIN
2025-06-19 13:47:33,620 - INFO - Updated user with database info: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T13:47:23.925485+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00008f79-0000-0d00-0000-685414ec0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750340844}
2025-06-19 13:47:33,620 - INFO - Returning authenticated user with role SUPER_ADMIN: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T13:47:23.925485+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00008f79-0000-0d00-0000-685414ec0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750340844}
2025-06-19 13:52:58,864 - INFO - RBAC client initialized successfully.
2025-06-19 13:52:58,868 - INFO - RBAC client initialized successfully.
2025-06-19 13:55:44,268 - INFO - RBAC log level set to DEBUG
2025-06-19 13:55:44,284 - INFO - RBAC log level set to DEBUG
2025-06-19 13:55:44,285 - INFO - RBAC log level set to DEBUG
2025-06-19 13:55:44,285 - INFO - RBAC log level set to DEBUG
2025-06-19 13:55:59,390 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 13:55:59,642 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 13:56:27,108 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 13:56:27,242 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 13:56:27,367 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 13:56:27,417 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 13:56:28,808 - INFO - RBAC client initialized successfully.
2025-06-19 13:56:28,969 - INFO - RBAC client initialized successfully.
2025-06-19 13:56:29,130 - INFO - RBAC client initialized successfully.
2025-06-19 13:56:29,334 - INFO - RBAC client initialized successfully.
2025-06-19 14:06:33,631 - INFO - RBAC client initialized successfully.
2025-06-19 14:06:33,648 - INFO - RBAC client initialized successfully.
2025-06-19 14:06:44,709 - INFO - RBAC client initialized successfully.
2025-06-19 14:06:44,726 - INFO - RBAC client initialized successfully.
2025-06-19 14:07:01,182 - INFO - RBAC client initialized successfully.
2025-06-19 14:07:01,202 - INFO - RBAC client initialized successfully.
2025-06-19 14:07:12,913 - INFO - RBAC client initialized successfully.
2025-06-19 14:07:16,484 - INFO - RBAC client initialized successfully.
2025-06-19 14:18:45,486 - INFO - RBAC log level set to DEBUG
2025-06-19 14:18:45,498 - INFO - RBAC log level set to DEBUG
2025-06-19 14:18:45,502 - INFO - RBAC log level set to DEBUG
2025-06-19 14:18:45,512 - INFO - RBAC log level set to DEBUG
2025-06-19 14:21:42,180 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:21:42,404 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:22:06,734 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:22:06,809 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:22:06,971 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:22:07,024 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:22:07,463 - INFO - RBAC client initialized successfully.
2025-06-19 14:22:07,703 - INFO - RBAC client initialized successfully.
2025-06-19 14:22:07,809 - INFO - RBAC client initialized successfully.
2025-06-19 14:22:07,887 - INFO - RBAC client initialized successfully.
2025-06-19 14:24:21,422 - INFO - RBAC log level set to DEBUG
2025-06-19 14:24:21,422 - INFO - RBAC log level set to DEBUG
2025-06-19 14:24:21,438 - INFO - RBAC log level set to DEBUG
2025-06-19 14:24:21,448 - INFO - RBAC log level set to DEBUG
2025-06-19 14:25:31,841 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:25:32,090 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:25:58,864 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:25:58,900 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:25:59,099 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:25:59,107 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:25:59,464 - INFO - RBAC client initialized successfully.
2025-06-19 14:25:59,530 - INFO - RBAC client initialized successfully.
2025-06-19 14:25:59,767 - INFO - RBAC client initialized successfully.
2025-06-19 14:25:59,857 - INFO - RBAC client initialized successfully.
2025-06-19 14:26:54,939 - INFO - RBAC client initialized successfully.
2025-06-19 14:26:55,003 - INFO - RBAC client initialized successfully.
2025-06-19 14:29:22,556 - INFO - RBAC client initialized successfully.
2025-06-19 14:29:22,557 - INFO - Found current user ID in header: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 14:29:22,557 - INFO - Using mock authentication with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 14:29:22,557 - INFO - get_mock_user returned: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150'}
2025-06-19 14:29:22,630 - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:26:55.461026+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"0000cf7d-0000-0d00-0000-68541e2f0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343215}
2025-06-19 14:29:22,631 - INFO - Set user role from database to: SUPER_ADMIN
2025-06-19 14:29:22,631 - INFO - Updated user with database info: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:26:55.461026+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"0000cf7d-0000-0d00-0000-68541e2f0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343215}
2025-06-19 14:29:22,632 - INFO - Returning authenticated user with role SUPER_ADMIN: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:26:55.461026+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"0000cf7d-0000-0d00-0000-68541e2f0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343215}
2025-06-19 14:34:32,401 - INFO - RBAC log level set to DEBUG
2025-06-19 14:34:32,402 - INFO - RBAC log level set to DEBUG
2025-06-19 14:34:32,410 - INFO - RBAC log level set to DEBUG
2025-06-19 14:34:32,424 - INFO - RBAC log level set to DEBUG
2025-06-19 14:34:44,593 - INFO - RBAC client initialized successfully.
2025-06-19 14:34:44,734 - INFO - RBAC client initialized successfully.
2025-06-19 14:35:01,545 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:35:01,770 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:35:28,250 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:35:28,320 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:35:28,464 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:35:28,483 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:35:29,095 - INFO - RBAC client initialized successfully.
2025-06-19 14:35:29,223 - INFO - RBAC client initialized successfully.
2025-06-19 14:35:41,766 - INFO - RBAC client initialized successfully.
2025-06-19 14:35:41,767 - INFO - Found current user ID in header: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 14:35:41,767 - INFO - Using mock authentication with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 14:35:41,768 - INFO - get_mock_user returned: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150'}
2025-06-19 14:35:41,835 - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:35:29.880934+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00006e7e-0000-0d00-0000-685420320000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343730}
2025-06-19 14:35:41,836 - INFO - Set user role from database to: SUPER_ADMIN
2025-06-19 14:35:41,837 - INFO - Updated user with database info: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:35:29.880934+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00006e7e-0000-0d00-0000-685420320000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343730}
2025-06-19 14:35:41,838 - INFO - Returning authenticated user with role SUPER_ADMIN: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:35:29.880934+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00006e7e-0000-0d00-0000-685420320000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343730}
2025-06-19 14:37:36,421 - INFO - RBAC client initialized successfully.
2025-06-19 14:37:36,443 - INFO - RBAC client initialized successfully.
2025-06-19 14:37:39,202 - INFO - RBAC client initialized successfully.
2025-06-19 14:37:39,202 - INFO - Found current user ID in header: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 14:37:39,202 - INFO - Using mock authentication with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 14:37:39,203 - INFO - get_mock_user returned: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150'}
2025-06-19 14:37:39,277 - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:37:37.180293+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"0000987e-0000-0d00-0000-685420b10000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343857}
2025-06-19 14:37:39,278 - INFO - Set user role from database to: SUPER_ADMIN
2025-06-19 14:37:39,278 - INFO - Updated user with database info: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:37:37.180293+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"0000987e-0000-0d00-0000-685420b10000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343857}
2025-06-19 14:37:39,279 - INFO - Returning authenticated user with role SUPER_ADMIN: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T14:37:37.180293+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"0000987e-0000-0d00-0000-685420b10000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750343857}
2025-06-19 14:48:52,687 - INFO - RBAC log level set to DEBUG
2025-06-19 14:48:52,689 - INFO - RBAC log level set to DEBUG
2025-06-19 14:48:52,705 - INFO - RBAC log level set to DEBUG
2025-06-19 14:48:52,706 - INFO - RBAC log level set to DEBUG
2025-06-19 14:48:59,488 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:49:00,061 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:49:25,729 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:49:25,738 - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-19 14:49:26,334 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:49:26,336 - INFO - RBAC client initialized successfully in development mode.
2025-06-19 14:49:27,051 - INFO - RBAC client initialized successfully.
2025-06-19 14:49:27,051 - INFO - RBAC client initialized successfully.
2025-06-19 14:49:27,503 - INFO - RBAC client initialized successfully.
2025-06-19 14:49:28,462 - INFO - RBAC client initialized successfully.
2025-06-19 14:51:46,061 - INFO - RBAC client initialized successfully.
2025-06-19 14:51:46,322 - INFO - RBAC client initialized successfully.
2025-06-19 14:52:22,599 - INFO - RBAC client initialized successfully.
2025-06-19 14:52:22,611 - INFO - RBAC client initialized successfully.
2025-06-19 14:52:22,680 - INFO - RBAC client initialized successfully.
2025-06-19 14:55:19,016 - INFO - RBAC client initialized successfully.
2025-06-19 14:55:19,019 - INFO - RBAC client initialized successfully.
2025-06-19 14:55:19,041 - INFO - RBAC client initialized successfully.
2025-06-19 14:55:37,381 - INFO - RBAC client initialized successfully.
2025-06-19 14:55:37,381 - INFO - RBAC client initialized successfully.
2025-06-19 14:58:17,584 - INFO - RBAC client initialized successfully.
2025-06-19 14:58:17,606 - INFO - RBAC client initialized successfully.
2025-06-19 14:58:22,940 - INFO - RBAC client initialized successfully.
2025-06-19 14:58:23,204 - INFO - RBAC client initialized successfully.
2025-06-19 14:59:04,947 - INFO - RBAC client initialized successfully.
2025-06-19 14:59:05,149 - INFO - RBAC client initialized successfully.
2025-06-19 14:59:16,850 - INFO - RBAC client initialized successfully.
2025-06-19 14:59:16,896 - INFO - RBAC client initialized successfully.
2025-06-19 14:59:17,120 - INFO - RBAC client initialized successfully.
2025-06-19 14:59:23,076 - INFO - RBAC client initialized successfully.
2025-06-19 15:05:43,851 - INFO - RBAC client initialized successfully.
2025-06-19 15:05:43,857 - INFO - RBAC client initialized successfully.
2025-06-19 15:05:50,384 - INFO - RBAC client initialized successfully.
2025-06-19 15:05:50,385 - INFO - Found current user ID in header: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 15:05:50,385 - INFO - Using mock authentication with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 15:05:50,386 - INFO - get_mock_user returned: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150'}
2025-06-19 15:05:50,455 - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T15:05:44.876612+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002680-0000-0d00-0000-685427490000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750345545}
2025-06-19 15:05:50,455 - INFO - Set user role from database to: SUPER_ADMIN
2025-06-19 15:05:50,456 - INFO - Updated user with database info: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T15:05:44.876612+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002680-0000-0d00-0000-685427490000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750345545}
2025-06-19 15:05:50,457 - INFO - Returning authenticated user with role SUPER_ADMIN: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T15:05:44.876612+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002680-0000-0d00-0000-685427490000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750345545}
2025-06-19 15:06:05,649 - INFO - RBAC client initialized successfully.
2025-06-19 15:06:05,650 - INFO - Found current user ID in header: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 15:06:05,650 - INFO - Using mock authentication with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-19 15:06:05,651 - INFO - get_mock_user returned: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150'}
2025-06-19 15:06:05,725 - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T15:05:44.876612+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002680-0000-0d00-0000-685427490000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750345545}
2025-06-19 15:06:05,725 - INFO - Set user role from database to: SUPER_ADMIN
2025-06-19 15:06:05,726 - INFO - Updated user with database info: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T15:05:44.876612+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002680-0000-0d00-0000-685427490000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750345545}
2025-06-19 15:06:05,726 - INFO - Returning authenticated user with role SUPER_ADMIN: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-19T15:05:44.876612+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002680-0000-0d00-0000-685427490000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750345545}
