// ./frontend/src/index.tsx
import React, { useEffect, useState, useRef } from 'react'
import ReactDOM from 'react-dom/client'
import { HashRouter, Route, Routes, Navigate, useNavigate, useLocation } from 'react-router-dom'
import { Msal<PERSON>rov<PERSON>, useMsal } from '@azure/msal-react'
import { EventType } from '@azure/msal-browser'
import { initializeAppIcons } from './utils/iconConfigSimple'
import '@fluentui/react/dist/css/fabric.min.css'

import Chat from './pages/chat/Chat'
import Layout from './pages/layout/Layout'
import ProjectLayout from './pages/layout/ProjectLayout'
import ProjectSelector from './pages/projects/ProjectSelector'
import NewProject from './pages/projects/NewProject'
import NoPage from './pages/NoPage'
import AdminPanel from './pages/admin/AdminPanel'
import DevTools from './pages/DevTools'
import Login from './pages/Login'
import AuthCallback from './pages/AuthCallback'
import AuthDone from './pages/AuthDone'
import AutoLogin from './pages/AutoLogin'
import { AppStateProvider } from './state/AppProvider'
import { UserProvider } from './state/UserProvider'
import { msalInstance } from './auth/msal-config'

import './index.css'

// Initialize MSAL before using it
(async () => {
  try {
    console.log('Initializing MSAL...');
    await msalInstance.initialize();
    console.log('MSAL initialized successfully');

    // Now handle redirect after initialization
    msalInstance.handleRedirectPromise()
      .then(response => {
        console.log('Index.tsx: Redirect handled successfully:', response);
        if (response) {
          console.log('Index.tsx: Authentication successful, account:', response.account);
          localStorage.setItem('isAuthenticated', 'true');

          // Set the active account
          msalInstance.setActiveAccount(response.account);
          localStorage.setItem('msalAccountSet', 'true'); // Indicate MSAL account is set

          // Clear any existing user data to ensure we get fresh data
          localStorage.removeItem('currentUser');

          // Import and clear user context cache
          import('./services/userContextService').then(module => {
            if (module.default && module.default.clearCache) {
              console.log('Index.tsx: Clearing user context cache after successful login');
              module.default.clearCache();
            }
          }).catch(err => {
            console.error('Index.tsx: Error importing userContextService:', err);
          });

          // After successful redirect and account is set, get ID token and send to backend
          const account = response.account;
          if (account) {
            msalInstance.acquireTokenSilent({
              scopes: ["openid", "profile", "email"], // Basic OIDC scopes for ID token
              account: account,
            }).then(tokenResponse => {
              if (tokenResponse && tokenResponse.idToken) {
                console.log('Index.tsx: Acquired ID token, sending to backend /api/auth/session-login');
                fetch('/api/auth/session-login', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ id_token: tokenResponse.idToken })
                }).then(backendResponse => {
                  if (backendResponse.ok) {
                    console.log('Index.tsx: Backend session established successfully.');
                    // Force reload the page to ensure we get fresh user data
                    if (window.location.hash !== '#/projects') {
                      console.log('Index.tsx: Redirecting to projects page after successful login');
                      window.location.href = '/#/projects';
                    } else {
                      console.log('Index.tsx: Already on projects page, forcing reload');
                      window.location.reload();
                    }
                  } else {
                    console.error('Index.tsx: Backend session establishment failed.', backendResponse);
                  }
                }).catch(fetchError => {
                  console.error('Index.tsx: Error sending ID token to backend:', fetchError);
                });
              }
            }).catch(error => {
              console.error('Index.tsx: acquireTokenSilent failed after redirect:', error);
              // Handle interaction required error if necessary
              // if (error instanceof InteractionRequiredAuthError) {
              //   msalInstance.acquireTokenRedirect({ scopes: ["openid", "profile", "email"] });
              // }
            });
          }
        }
      })
      .catch(error => {
        console.error('Index.tsx: Error handling redirect:', error);
      });
  } catch (error) {
    console.error('Error initializing MSAL:', error);
  }
})();

// Initialize Fluent UI icons with local fonts to avoid CDN/CORS issues
initializeAppIcons();

// Authentication wrapper component
const AuthenticatedRoute: React.FC<{ element: React.ReactNode }> = ({ element }) => {
  const { accounts, inProgress } = useMsal();
  const navigate = useNavigate();
  const location = useLocation();
  const [isChecking, setIsChecking] = useState(true);
  const hasRedirected = useRef(false);

  useEffect(() => {
    // If MSAL is in the middle of an operation, wait
    if (inProgress !== 'none') {
      console.log('MSAL operation in progress, waiting...');
      return;
    }

    // Skip authentication check if we're already on the login page
    if (location.pathname === '/login') {
      setIsChecking(false);
      return;
    }

    console.log('AuthenticatedRoute - Checking authentication status');

    // Simplified authentication check
    const checkAuth = async () => {
      try {
        // In production with Azure App Service auth, we can assume the user is authenticated
        // if they've reached this point, as the App Service would have redirected them to login
        if (window.location.hostname !== 'localhost') {
          console.log('Production environment - assuming user is authenticated via Azure App Service');
          localStorage.setItem('isAuthenticated', 'true');
          setIsChecking(false);
          return;
        }

        // For development environment, check MSAL and localStorage
        if (accounts.length > 0) {
          console.log('User is authenticated with MSAL');
          setIsChecking(false);
          return;
        }

        const storedAuthState = localStorage.getItem('isAuthenticated');
        if (storedAuthState === 'true') {
          console.log('User is authenticated via localStorage');
          setIsChecking(false);
          return;
        }

        // If not authenticated in development, redirect to login
        if (!hasRedirected.current) {
          console.log('User is not authenticated, redirecting to login');
          hasRedirected.current = true;
          navigate('/login');
        }
      } catch (error) {
        console.error('Auth check error:', error);
        if (!hasRedirected.current) {
          hasRedirected.current = true;
          navigate('/login');
        }
      } finally {
        setIsChecking(false);
      }
    };

    // Reset the redirect flag when the location changes
    hasRedirected.current = false;

    // Only check auth if MSAL is not in the middle of an operation
    if (inProgress === 'none') {
      checkAuth();
    }
  }, [accounts, navigate, location, inProgress]);

  // Show loading indicator while checking authentication
  if (isChecking) {
    return <div className="auth-loading">Loading authentication status...</div>;
  }

  // Simplified authentication check
  const isAuthenticated =
    window.location.hostname !== 'localhost' || // In production, assume authenticated
    accounts.length > 0 ||                      // MSAL authenticated
    localStorage.getItem('isAuthenticated') === 'true'; // localStorage authenticated

  if (isAuthenticated) {
    return <>{element}</>;
  }

  // Otherwise, render nothing (we should be redirecting to login)
  return null;
};

export default function App() {
  return (
    <MsalProvider instance={msalInstance}>
      <AppStateProvider>
        <UserProvider>
          <HashRouter>
            <Routes>
              {/* Login route */}
              <Route path="/login" element={<Login />} />

              {/* Auth callback routes */}
              <Route path="/auth/callback" element={<AuthCallback />} />
              <Route path="/auth/done" element={<AuthDone />} />

              {/* Root route - Direct to projects in production, AutoLogin in development */}
              <Route path="/" element={
                window.location.hostname === 'localhost'
                  ? <AutoLogin />
                  : <Navigate to="/projects" />
              } />

              {/* Protected routes */}
              <Route path="/projects" element={<AuthenticatedRoute element={<ProjectSelector />} />} />
              <Route path="/new-project" element={<AuthenticatedRoute element={<NewProject />} />} />

              {/* Project-specific routes */}
              <Route path="/project/:projectId" element={<AuthenticatedRoute element={<ProjectLayout />} />}>
                <Route index element={<Chat />} />
              </Route>

              {/* Admin Panel Routes */}
              <Route path="/admin" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/users" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/global-settings" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/global-costs" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/project-settings" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/projects" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/teams" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/tags" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/regional-admins" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/regions" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/regional-costs" element={<AuthenticatedRoute element={<AdminPanel />} />} />
              <Route path="/admin/cost-analytics" element={<AuthenticatedRoute element={<AdminPanel />} />} />

              {/* Development Tools - Only available in development mode */}
              {(import.meta.env.DEVELOPMENT_MODE?.toLowerCase() === 'true' ||
                import.meta.env.DEV === true ||
                window.location.hostname === 'localhost') && (
                <Route path="/dev" element={<AuthenticatedRoute element={<DevTools />} />} />
              )}

              {/* Legacy route for backward compatibility */}
              <Route path="/legacy" element={<AuthenticatedRoute element={<Layout />} />}>
                <Route index element={<Chat />} />
              </Route>

              <Route path="*" element={<NoPage />} />
            </Routes>
          </HashRouter>
        </UserProvider>
      </AppStateProvider>
    </MsalProvider>
  )
}

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)
