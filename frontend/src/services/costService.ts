// frontend/src/services/costService.ts
import axios from 'axios';
import { UserRole } from '../models/roles';
import { getAuthHeaders } from './authHeaderService';
import {
  getCachedCostData,
  cacheCostData,
  clearCostCache as clearCostCacheUtil
} from '../utils/costCache';

// Types for cost data returned from the cost APIs.  The backend aggregates
// spending by project, service, region and resource group so the frontend can
// easily display detailed breakdowns.
export interface ProjectCost {
  project: string;
  projectId: string;
  cost: number;
  budget: number;
  region: string;
  regionId: string;
}

export interface ServiceCost {
  service: string;
  cost: number;
  isShared: boolean;
}

export interface ResourceCost {
  name: string;
  resourceId: string;
  resourceType: string; // 'storage', 'search', 'function', etc.
  cost: number;
  isShared: boolean;
  region: string;
  regionId: string;
}

export interface ContainerCost {
  name: string;
  storageAccount: string;
  cost: number;
  projectId: string;
  project: string;
  region: string;
  regionId: string;
}

export interface IndexerCost {
  name: string;
  searchService: string;
  cost: number;
  projectId: string;
  project: string;
  region: string;
  regionId: string;
}

export interface RegionCost {
  region: string;
  regionId: string;
  cost: number;
  budget?: number;
}

export interface ResourceGroupCost {
  resourceGroup: string;
  cost: number;
}

export interface CostWarning {
  message: string;
  severity: 'warning' | 'severe' | 'error';
  lastUpdate: string;
  hoursOld?: number;
}

export interface CostData {
  projectCosts: ProjectCost[];
  serviceCosts: ServiceCost[];
  resourceCosts: ResourceCost[];
  containerCosts: ContainerCost[];
  indexerCosts: IndexerCost[];
  regionCosts: RegionCost[];
  resourceGroupCosts: ResourceGroupCost[];
  totalCost: number;
  warning?: CostWarning;
}

export interface CostFilterOptions {
  timeRange: string;
  regionId?: string;
  projectId?: string;
  resourceType?: string;
  showSharedResources: boolean;
}

// Cost service API
const costService = {
  // Get cost data for a specific time range and filters
  getCostData: async (
    timeRange: string = 'month',
    userRole: UserRole = UserRole.SUPER_ADMIN,
    userRegionId?: string,
    filters: Partial<CostFilterOptions> = {}
  ): Promise<CostData> => {
    try {
      // Check cache first
      const cachedData = getCachedCostData(timeRange, String(userRole), userRegionId, filters);
      if (cachedData) {
        console.log('📦 Returning cached cost data');
        return cachedData;
      }

      const params: any = { timeRange };

      const effectiveRegion =
        userRole === UserRole.REGIONAL_ADMIN ? userRegionId : filters.regionId;
      if (effectiveRegion) params.region_id = effectiveRegion;
      if (filters.projectId) params.project_id = filters.projectId;
      if (filters.resourceType) params.resourceType = filters.resourceType;
      if (filters.showSharedResources !== undefined)
        params.includeShared = filters.showSharedResources;

      const authHeaders = await getAuthHeaders();
      // Convert HeadersInit to a plain object for Axios
      const headers = Object.fromEntries(
        Object.entries(authHeaders).map(([key, value]) => [key, String(value)])
      );

      console.log('🔍 Cost API Request Parameters:', params);
      console.log('🔍 Cost API Request Headers:', headers);

      const [overviewRes, resourcesRes] = await Promise.all([
        axios.get('/api/cost/overview', { params, headers }),
        axios.get('/api/cost/resources', { params, headers })
      ]);

      console.log('📊 Cost Overview API Response:', overviewRes.data);
      console.log('🔧 Cost Resources API Response:', resourcesRes.data);

      // Check for warning in the response
      const warning = overviewRes.data.warning || resourcesRes.data.warning;

      const costData: CostData = {
        projectCosts: overviewRes.data.projectCosts || [],
        serviceCosts: overviewRes.data.serviceCosts || [],
        resourceCosts: resourcesRes.data.resources || [],
        containerCosts: [],
        indexerCosts: [],
        regionCosts: overviewRes.data.regionCosts || [],
        resourceGroupCosts: overviewRes.data.resourceGroupCosts || [],
        totalCost: overviewRes.data.totalCost || 0,
        ...(warning && { warning })
      };

      console.log('💰 Processed Cost Data:', costData);
      
      // Cache the data
      cacheCostData(costData, timeRange, userRole, userRegionId, filters);
      console.log('📈 Total Cost:', costData.totalCost);
      console.log('📋 Project Costs Count:', costData.projectCosts.length);
      console.log('🔧 Service Costs Count:', costData.serviceCosts.length);
      console.log('🌍 Region Costs Count:', costData.regionCosts.length);
      console.log('🏷️ Resource Group Costs Count:', costData.resourceGroupCosts.length);
      console.log('⚙️ Resource Costs Count:', costData.resourceCosts.length);

      return costData;
    } catch (err) {
      console.error('Error fetching cost data', err);
      throw err;
    }
  },

  // Get cost data for a specific project
  getProjectCostData: async (
    projectId: string,
    timeRange: string = 'month',
    userRole: UserRole = UserRole.SUPER_ADMIN,
    userRegionId?: string
  ): Promise<CostData> => {
    return costService.getCostData(timeRange, userRole, userRegionId, { projectId });
  },

  // Trigger immediate cost data collection
  collectCostDataNow: async (): Promise<void> => {
    const authHeaders = await getAuthHeaders();
    const headers = Object.fromEntries(
      Object.entries(authHeaders).map(([key, value]) => [key, String(value)])
    );
    await axios.post('/api/cost/collect-now', {}, { headers });
    // Clear cache after collecting new data
    clearCostCacheUtil();
  },

  // Clear the cost data cache
  clearCostCache: () => {
    clearCostCacheUtil();
  }
};

export default costService;
