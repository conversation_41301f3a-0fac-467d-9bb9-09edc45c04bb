/* frontend/src/index.css */
/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', 'Apple Color Emoji', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

a {
  color: #0078d4;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
}

/* Global utility classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.text-center {
  text-align: center;
}

/* Form elements */
input, select, textarea {
  font-family: inherit;
  font-size: inherit;
}

/* Set up root container for React */
#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Remove margins from all Fluent UI ProgressIndicator components */
.ms-ProgressIndicator,
div[class*="ms-ProgressIndicator"],
div[class*="root-"][class*="ms-ProgressIndicator"] {
  margin: 0 !important;
}

.ms-ProgressIndicator-itemProgress,
div[class*="itemProgress-"] {
  margin: 0 !important;
}

.ms-ProgressIndicator-progressTrack,
div[class*="progressTrack-"] {
  margin: 0 !important;
}

.ms-ProgressIndicator-progressBar,
div[class*="progressBar-"] {
  margin: 0 !important;
}

/* Remove margins from any wrapper divs around ProgressIndicator */
div[class*="_deploymentProgressBar_"],
div[class*="_inProgressProgressBar_"],
div[class*="_failedProgressBar_"] {
  margin: 0 !important;
}

/* Ensure emoji support */
.emoji, 
[class*="projectIcon"],
[class*="icon"] {
  font-family: 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', 'Apple Color Emoji', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  /* font-variant-emoji: emoji; - not yet standard, removed to avoid warnings */
  font-weight: normal;
  font-style: normal;
}

/* Override for elements that contain "Icon" (capital I) in class name to use default font */
/* This has higher specificity and will override the above rule */
/* EXCEPTION: Don't override Fluent UI icons (ms-Icon) */
[class*="Icon"]:not([class*="ms-Icon"]) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', 'Apple Color Emoji', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif !important;
}

/* Ensure Fluent UI icons use the correct font */
.ms-Icon {
  font-family: 'FabricMDL2Icons' !important;
  font-style: normal !important;
  font-weight: normal !important;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
